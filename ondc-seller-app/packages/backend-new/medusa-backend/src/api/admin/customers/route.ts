import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { centralizedDb } from '../../../services/centralized-database';

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  console.log(`🚀 [TENANT FILTER] === ENHANCED CUSTOMERS ENDPOINT CALLED ===`);

  try {
    // Extract tenant ID from header
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    console.log(`🔍 [TENANT FILTER] Processing customers request for tenant: ${tenantId}`);

    // Get query parameters
    const {
      limit = 50,
      offset = 0,
      // fields, order, and filters are available but not used in direct DB query
    } = req.query;

    let customers: any[] = [];
    let count = 0;

    try {
      console.log(`🔗 [TENANT FILTER] Using centralized database service`);

      // Get total count for this tenant using centralized service
      count = await centralizedDb.getCount('customer', tenantId);
      console.log(`📊 [TENANT FILTER] Total customers for tenant ${tenantId}: ${count}`);

      // Get customers using centralized service
      const customersResult = await centralizedDb.getCustomers(tenantId, {
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
      });

      customers = customersResult.rows || [];
      console.log(`👥 [TENANT FILTER] Retrieved ${customers.length} customers`);
    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error:', dbError);
      customers = [];
      count = 0;
    }
    // No finally block needed - centralized service handles connection cleanup

    // Return response in Medusa format
    const response = {
      customers,
      count: customers.length,
      offset: parseInt(offset as string),
      limit: parseInt(limit as string),
      _tenant: {
        id: tenantId,
        filtered: true,
        method: 'centralized_db_service',
        total_in_db: count,
      },
    };

    // Add tenant headers for debugging
    res.setHeader('X-Tenant-ID', tenantId);
    res.setHeader('X-Tenant-Filtered', 'true');
    res.setHeader('X-Customers-Count', customers.length.toString());

    console.log(
      `📤 [TENANT FILTER] Returning ${customers.length} customers for tenant ${tenantId}`
    );

    res.json(response);
  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error in customers endpoint:', error);

    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    res.status(500).json({
      error: 'Failed to fetch customers',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'tenant_filter_error',
        timestamp: new Date().toISOString(),
      },
    });
  }
}

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  console.log(`🚀 [TENANT FILTER] === CUSTOM CUSTOMERS POST ENDPOINT CALLED ===`);

  try {
    // Extract tenant ID from header
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    console.log(`🔍 [TENANT FILTER] Creating customer for tenant: ${tenantId}`);

    // Get customer data from request body
    const customerData = req.body as any;

    // Inject tenant_id into the customer data
    const customerWithTenant = {
      ...customerData,
      tenant_id: tenantId,
      metadata: {
        ...customerData.metadata,
        tenant_id: tenantId,
      },
    };

    console.log(`🏷️ [TENANT FILTER] Injected tenant_id: ${tenantId} into customer data`);

    // Direct database connection approach
    const { Client } = require('pg');
    const client = new Client({
      connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
    });

    let createdCustomer: any = null;

    try {
      await client.connect();

      // Generate a unique customer ID
      const customerId = `cus_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // Insert customer with tenant_id
      const insertQuery = `
        INSERT INTO customer (
          id, email, first_name, last_name, phone,
          tenant_id, metadata, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
        RETURNING *
      `;

      const values = [
        customerId,
        customerWithTenant.email,
        customerWithTenant.first_name || '',
        customerWithTenant.last_name || '',
        customerWithTenant.phone || null,
        tenantId,
        JSON.stringify(customerWithTenant.metadata || {}),
      ];

      const result = await client.query(insertQuery, values);
      createdCustomer = result.rows[0];

      console.log(
        `✅ [TENANT FILTER] Created customer with ID: ${createdCustomer.id} for tenant: ${tenantId}`
      );
    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error:', dbError);
      throw dbError;
    } finally {
      // Always close the database connection
      try {
        await client.end();
        console.log(`🔗 [TENANT FILTER] Database connection closed`);
      } catch (closeError) {
        console.error('❌ [TENANT FILTER] Error closing database connection:', closeError);
      }
    }

    // Return response
    const response = {
      customer: createdCustomer,
      _tenant: {
        id: tenantId,
        injected: true,
        method: 'direct_db_connection',
      },
    };

    res.setHeader('X-Tenant-ID', tenantId);
    res.setHeader('X-Tenant-Injected', 'true');

    res.status(201).json(response);
  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error creating customer:', error);

    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    res.status(500).json({
      error: 'Failed to create customer',
      message: error.message,
      tenant_id: tenantId,
    });
  }
}
