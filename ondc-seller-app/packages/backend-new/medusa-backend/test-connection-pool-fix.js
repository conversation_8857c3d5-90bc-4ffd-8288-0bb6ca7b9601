#!/usr/bin/env node

/**
 * Connection Pool Fix Verification Test
 * 
 * This script tests the API endpoints with multiple consecutive calls
 * to verify that the connection pool exhaustion issue has been resolved.
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:9001';
const TENANT_ID = 'tenant-electronics-001';

// Test configuration
const TEST_CONFIG = {
  consecutiveCalls: 50, // Number of consecutive calls to make
  concurrentBatches: 5, // Number of concurrent batches
  delayBetweenCalls: 100, // Delay between calls in ms
};

// API endpoints to test
const ENDPOINTS = [
  { name: 'Store Products', url: '/store/products', method: 'GET' },
  { name: 'Admin Products', url: '/admin/products', method: 'GET' },
  { name: 'Admin Customers', url: '/admin/customers', method: 'GET' },
  { name: 'Store Products with Filter', url: '/store/products?limit=10', method: 'GET' },
  { name: 'Admin Products with Filter', url: '/admin/products?limit=10&offset=0', method: 'GET' },
];

// Test results tracking
const testResults = {
  totalRequests: 0,
  successfulRequests: 0,
  failedRequests: 0,
  errors: [],
  responseTimes: [],
  startTime: null,
  endTime: null,
};

/**
 * Make a single API request
 */
async function makeRequest(endpoint, requestNumber) {
  const startTime = Date.now();
  
  try {
    const response = await axios({
      method: endpoint.method,
      url: `${BASE_URL}${endpoint.url}`,
      headers: {
        'x-tenant-id': TENANT_ID,
        'Content-Type': 'application/json',
      },
      timeout: 30000, // 30 second timeout
    });

    const responseTime = Date.now() - startTime;
    testResults.responseTimes.push(responseTime);
    testResults.successfulRequests++;

    console.log(`✅ [${requestNumber}] ${endpoint.name}: ${response.status} (${responseTime}ms)`);
    
    return {
      success: true,
      status: response.status,
      responseTime,
      dataSize: JSON.stringify(response.data).length,
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    testResults.failedRequests++;
    
    const errorInfo = {
      endpoint: endpoint.name,
      requestNumber,
      error: error.message,
      status: error.response?.status,
      responseTime,
    };
    
    testResults.errors.push(errorInfo);
    
    console.error(`❌ [${requestNumber}] ${endpoint.name}: ${error.message} (${responseTime}ms)`);
    
    return {
      success: false,
      error: error.message,
      status: error.response?.status,
      responseTime,
    };
  }
}

/**
 * Test consecutive calls to a single endpoint
 */
async function testConsecutiveCalls(endpoint) {
  console.log(`\n🚀 Testing ${TEST_CONFIG.consecutiveCalls} consecutive calls to ${endpoint.name}`);
  console.log('='.repeat(60));
  
  const results = [];
  
  for (let i = 1; i <= TEST_CONFIG.consecutiveCalls; i++) {
    testResults.totalRequests++;
    const result = await makeRequest(endpoint, i);
    results.push(result);
    
    // Small delay between requests
    if (TEST_CONFIG.delayBetweenCalls > 0) {
      await new Promise(resolve => setTimeout(resolve, TEST_CONFIG.delayBetweenCalls));
    }
  }
  
  return results;
}

/**
 * Test concurrent requests
 */
async function testConcurrentRequests(endpoint) {
  console.log(`\n🚀 Testing ${TEST_CONFIG.concurrentBatches} concurrent batches to ${endpoint.name}`);
  console.log('='.repeat(60));
  
  const promises = [];
  
  for (let batch = 1; batch <= TEST_CONFIG.concurrentBatches; batch++) {
    const batchPromises = [];
    
    for (let i = 1; i <= 10; i++) { // 10 requests per batch
      testResults.totalRequests++;
      const requestNumber = `B${batch}-${i}`;
      batchPromises.push(makeRequest(endpoint, requestNumber));
    }
    
    promises.push(Promise.all(batchPromises));
  }
  
  const results = await Promise.all(promises);
  return results.flat();
}

/**
 * Calculate and display test statistics
 */
function displayTestResults() {
  testResults.endTime = Date.now();
  const totalDuration = testResults.endTime - testResults.startTime;
  
  console.log('\n📊 TEST RESULTS SUMMARY');
  console.log('='.repeat(60));
  console.log(`Total Requests: ${testResults.totalRequests}`);
  console.log(`Successful Requests: ${testResults.successfulRequests}`);
  console.log(`Failed Requests: ${testResults.failedRequests}`);
  console.log(`Success Rate: ${((testResults.successfulRequests / testResults.totalRequests) * 100).toFixed(2)}%`);
  console.log(`Total Duration: ${totalDuration}ms (${(totalDuration / 1000).toFixed(2)}s)`);
  
  if (testResults.responseTimes.length > 0) {
    const avgResponseTime = testResults.responseTimes.reduce((a, b) => a + b, 0) / testResults.responseTimes.length;
    const minResponseTime = Math.min(...testResults.responseTimes);
    const maxResponseTime = Math.max(...testResults.responseTimes);
    
    console.log(`Average Response Time: ${avgResponseTime.toFixed(2)}ms`);
    console.log(`Min Response Time: ${minResponseTime}ms`);
    console.log(`Max Response Time: ${maxResponseTime}ms`);
  }
  
  if (testResults.errors.length > 0) {
    console.log('\n❌ ERRORS ENCOUNTERED:');
    console.log('='.repeat(60));
    testResults.errors.forEach((error, index) => {
      console.log(`${index + 1}. [${error.requestNumber}] ${error.endpoint}: ${error.error}`);
    });
  }
  
  // Determine if the test passed
  const successRate = (testResults.successfulRequests / testResults.totalRequests) * 100;
  const testPassed = successRate >= 95; // 95% success rate required
  
  console.log('\n🎯 TEST VERDICT');
  console.log('='.repeat(60));
  if (testPassed) {
    console.log('✅ CONNECTION POOL FIX SUCCESSFUL!');
    console.log('   All endpoints are handling high-frequency requests properly.');
  } else {
    console.log('❌ CONNECTION POOL ISSUES STILL EXIST!');
    console.log('   Some endpoints are still failing under load.');
  }
  
  return testPassed;
}

/**
 * Main test execution
 */
async function runTests() {
  console.log('🧪 CONNECTION POOL FIX VERIFICATION TEST');
  console.log('='.repeat(60));
  console.log(`Base URL: ${BASE_URL}`);
  console.log(`Tenant ID: ${TENANT_ID}`);
  console.log(`Consecutive Calls: ${TEST_CONFIG.consecutiveCalls}`);
  console.log(`Concurrent Batches: ${TEST_CONFIG.concurrentBatches}`);
  console.log('='.repeat(60));
  
  testResults.startTime = Date.now();
  
  try {
    // Test each endpoint with consecutive calls
    for (const endpoint of ENDPOINTS) {
      await testConsecutiveCalls(endpoint);
    }
    
    // Test concurrent requests on the most critical endpoint
    await testConcurrentRequests(ENDPOINTS[0]); // Store Products
    
    // Display results
    const testPassed = displayTestResults();
    
    process.exit(testPassed ? 0 : 1);
    
  } catch (error) {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n⚠️ Test interrupted by user');
  displayTestResults();
  process.exit(1);
});

// Run the tests
if (require.main === module) {
  runTests();
}

module.exports = { runTests, testResults };
