'use client';

import { useState, useCallback, useEffect } from 'react';
import { 
  cartAPI, 
  Cart, 
  Region, 
  AddToCartPayload,
  getStoredCartId, 
  setStoredCartId, 
  removeStoredCartId,
  calculateCartMetadata
} from '@/lib/api/cart';
import { useToast } from '@/app/providers/toast-provider';

export interface AddToCartItem {
  productId: string;
  variantId: string;
  quantity: number;
  product: {
    id: string;
    title: string;
    price: number;
    image: string;
    variant: string;
  };
}

interface UseApiCartReturn {
  cart: Cart | null;
  isOpen: boolean;
  isLoading: boolean;
  region: Region | null;
  addToCart: (item: AddToCartItem) => Promise<void>;
  removeItem: (lineItemId: string) => Promise<void>;
  updateQuantity: (lineItemId: string, quantity: number) => Promise<void>;
  clearCart: () => Promise<void>;
  openCart: () => void;
  closeCart: () => void;
  toggleCart: () => void;
  refreshCart: () => Promise<void>;
  initializeCart: () => Promise<void>;
}

export function useApiCart(storeHandle:string): UseApiCartReturn {
  const { showToast } = useToast();
  const [cart, setCart] = useState<Cart | null>(null);
  const [region, setRegion] = useState<Region | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Initialize cart - get region and existing cart
  const initializeCart = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // Get Asia region
      const regionsResponse = await cartAPI.getRegions('Asia',storeHandle);
      const asiaRegion = regionsResponse.regions.find(r => 
        r.name.toLowerCase().includes('asia')
      ) || regionsResponse.regions[0]; // Fallback to first region
      
      if (asiaRegion) {
        setRegion(asiaRegion);
        
        // Check for existing cart
        const existingCartId = getStoredCartId();
        if (existingCartId) {
          try {
            const cartResponse = await cartAPI.getCart(existingCartId,storeHandle);
            setCart(cartResponse.cart);
          } catch (error) {
            console.warn('Failed to load existing cart, will create new one:', error);
            removeStoredCartId();
          }
        }
      }
    } catch (error) {
      console.error('Failed to initialize cart:', error);
      showToast('Failed to initialize cart', 'error');
    } finally {
      setIsLoading(false);
    }
  }, [showToast, storeHandle]);

  // Initialize cart and region on mount
  useEffect(() => {
    initializeCart();
  }, [initializeCart]);

  // Create cart if it doesn't exist
  const ensureCartExists = useCallback(async (): Promise<string> => {
    if (cart?.id) {
      return cart.id;
    }

    if (!region) {
      throw new Error('Region not available');
    }

    try {
      const cartResponse = await cartAPI.createCart({
        region_id: region.id,
      },storeHandle);
      
      setCart(cartResponse.cart);
      setStoredCartId(cartResponse.cart.id);
      
      return cartResponse.cart.id;
    } catch (error) {
      console.error('Failed to create cart:', error);
      throw new Error('Failed to create cart');
    }
  }, [cart, region]);

  // Add item to cart
  const addToCart = useCallback(async (item: AddToCartItem) => {
    try {
      setIsLoading(true);
      
      // Ensure cart exists
      const cartId = await ensureCartExists();
      
      // Calculate metadata for the item
      const metadata = calculateCartMetadata(
        item.product.price, 
        item.quantity,
        region?.tax_rate || 0.08
      );
      
      // Prepare payload
      const payload: AddToCartPayload = {
        variant_id: item.variantId,
        quantity: item.quantity,
        metadata,
      };
      
      // Add item to cart
      const cartResponse = await cartAPI.addLineItem(cartId, payload,storeHandle);
      setCart(cartResponse.cart);
      
      // Open cart sidebar
      setIsOpen(true);
      
      showToast('Product added to cart successfully!', 'success');
    } catch (error) {
      console.error('Failed to add item to cart:', error);
      showToast('Failed to add product to cart', 'error');
    } finally {
      setIsLoading(false);
    }
  }, [ensureCartExists, region, showToast]);

  // Remove item from cart
  const removeItem = useCallback(async (lineItemId: string) => {
    if (!cart?.id) return;
    
    try {
      setIsLoading(true);
      
      const cartResponse = await cartAPI.removeLineItem(cart.id, lineItemId,storeHandle);
      setCart(cartResponse.cart);
      
      showToast('Item removed from cart', 'success');
    } catch (error) {
      console.error('Failed to remove item:', error);
      showToast('Failed to remove item from cart', 'error');
    } finally {
      setIsLoading(false);
    }
  }, [cart, showToast]);

  // Update item quantity
  const updateQuantity = useCallback(async (lineItemId: string, quantity: number) => {
    if (!cart?.id) return;
    
    if (quantity <= 0) {
      await removeItem(lineItemId);
      return;
    }
    
    try {
      setIsLoading(true);
      
      const cartResponse = await cartAPI.updateLineItem(cart.id, lineItemId, { quantity },storeHandle);
      setCart(cartResponse.cart);
    } catch (error) {
      console.error('Failed to update quantity:', error);
      showToast('Failed to update quantity', 'error');
    } finally {
      setIsLoading(false);
    }
  }, [cart, removeItem, showToast]);

  // Clear cart
  const clearCart = useCallback(async () => {
    try {
      setIsLoading(true);
      
      if (cart?.id) {
        // Remove all items
        for (const item of cart.items) {
          await cartAPI.removeLineItem(cart.id, item.id,storeHandle);
        }
      }
      
      // Reset cart state
      setCart(null);
      removeStoredCartId();
      
      showToast('Cart cleared', 'success');
    } catch (error) {
      console.error('Failed to clear cart:', error);
      showToast('Failed to clear cart', 'error');
    } finally {
      setIsLoading(false);
    }
  }, [cart, showToast]);

  // Refresh cart from API
  const refreshCart = useCallback(async () => {
    const cartId = getStoredCartId();
    if (!cartId) return;
    
    try {
      setIsLoading(true);
      const cartResponse = await cartAPI.getCart(cartId,storeHandle);
      setCart(cartResponse.cart);
    } catch (error) {
      console.error('Failed to refresh cart:', error);
      // If cart doesn't exist, remove from storage
      removeStoredCartId();
      setCart(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Cart visibility controls
  const openCart = useCallback(() => setIsOpen(true), []);
  const closeCart = useCallback(() => setIsOpen(false), []);
  const toggleCart = useCallback(() => setIsOpen(prev => !prev), []);

  // Close cart on escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        closeCart();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, closeCart]);

  // Prevent body scroll when cart is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  return {
    cart,
    isOpen,
    isLoading,
    region,
    addToCart,
    removeItem,
    updateQuantity,
    clearCart,
    openCart,
    closeCart,
    toggleCart,
    refreshCart,
    initializeCart,
  };
}
