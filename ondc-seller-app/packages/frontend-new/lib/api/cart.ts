'use client';

// Types
export interface Region {
  id: string;
  name: string;
  currency_code: string;
  tax_rate: number;
  countries: Country[];
}

export interface Country {
  id: string;
  name: string;
  iso_2: string;
  iso_3: string;
}

export interface CartItem {
  id: string;
  title: string;
  description?: string;
  thumbnail?: string;
  variant_id: string;
  quantity: number;
  unit_price: number;
  total: number;
  metadata?: {
    total_amount?: number;
    total_tax?: number;
    shipping_charges?: number;
    grand_total?: number;
  };
}

export interface Cart {
  id: string;
  region_id: string;
  region: Region;
  items: CartItem[];
  total: number;
  subtotal: number;
  tax_total: number;
  shipping_total: number;
  created_at: string;
  updated_at: string;
}

export interface CreateCartPayload {
  region_id: string;
}

export interface AddToCartPayload {
  variant_id: string;
  quantity: number;
  metadata: {
    total_amount: number;
    total_tax: number;
    shipping_charges: number;
    grand_total: number;
  };
}

export interface UpdateLineItemPayload {
  quantity: number;
}

// API Response Types
export interface RegionsResponse {
  regions: Region[];
}

export interface CartResponse {
  cart: Cart;
}

// Local Storage Keys
const CART_ID_KEY = 'medusa_cart_id';

// Local Storage Helpers
export const getStoredCartId = (): string | null => {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem(CART_ID_KEY);
};

export const setStoredCartId = (cartId: string): void => {
  if (typeof window === 'undefined') return;
  localStorage.setItem(CART_ID_KEY, cartId);
};

export const removeStoredCartId = (): void => {
  if (typeof window === 'undefined') return;
  localStorage.removeItem(CART_ID_KEY);
};

// Utility Functions
export const calculateCartMetadata = (
  unitPrice: number, 
  quantity: number, 
  taxRate: number = 0.08,
  shippingRate: number = 0
) => {
  const totalAmount = unitPrice * quantity;
  const totalTax = totalAmount * taxRate;
  const shippingCharges = totalAmount > 5000 ? 0 : shippingRate;
  const grandTotal = totalAmount + totalTax + shippingCharges;
  
  return {
    total_amount: Math.round(totalAmount * 100) / 100,
    total_tax: Math.round(totalTax * 100) / 100,
    shipping_charges: Math.round(shippingCharges * 100) / 100,
    grand_total: Math.round(grandTotal * 100) / 100,
  };
};

// API Configuration
const getBackendUrl = (): string => {
  return process.env.NEXT_PUBLIC_MEDUSA_BASE_URL || 'http://localhost:9000';
};

const getHeaders = (storeHandle:string): HeadersInit => {
  return {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'x-tenant-id': storeHandle||'default',
    'x-publishable-api-key': process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY || ''
  };
};

// API Error Handler
const handleApiError = async (response: Response) => {
  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
    
    try {
      const errorData = await response.json();
      errorMessage = errorData.message || errorData.error || errorMessage;
    } catch {
      // If response is not JSON, use status text
    }
    
    throw new Error(errorMessage);
  }
};

// Cart API Class
class CartAPI {
  private baseUrl: string;
  
  constructor() {
    this.baseUrl = getBackendUrl();
  }
  
  // Get regions
  async getRegions(name?: string,storeHandle:string): Promise<RegionsResponse> {
    const url = new URL(`${this.baseUrl}/store/regions`);
    if (name) {
      url.searchParams.append('name', name);
    }
    
    console.log('🌍 Fetching regions:', url.toString());
    
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: getHeaders(storeHandle),
    });
    
    await handleApiError(response);
    const data = await response.json();
    
    console.log('✅ Regions response:', data);
    return data;
  }
  
  // Create cart
  async createCart(payload: CreateCartPayload,storeHandle:string): Promise<CartResponse> {
    const url = `${this.baseUrl}/store/carts`;
    
    console.log('🛒 Creating cart:', { url, payload });
    
    const response = await fetch(url, {
      method: 'POST',
      headers: getHeaders(storeHandle),
      body: JSON.stringify(payload),
    });
    
    await handleApiError(response);
    const data = await response.json();
    
    console.log('✅ Cart created:', data);
    return data;
  }
  
  // Get cart
  async getCart(cartId: string,storeHandle:string): Promise<CartResponse> {
    const url = `${this.baseUrl}/store/carts/${cartId}`;
    
    console.log('📦 Fetching cart:', url);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: getHeaders(storeHandle),
    });
    
    await handleApiError(response);
    const data = await response.json();
    
    console.log('✅ Cart fetched:', data);
    return data;
  }
  
  // Add line item to cart
  async addLineItem(cartId: string, payload: AddToCartPayload,storeHandle:string): Promise<CartResponse> {
    const url = `${this.baseUrl}/store/carts/${cartId}/line-items`;
    
    console.log('➕ Adding line item:', { url, payload });
    
    const response = await fetch(url, {
      method: 'POST',
      headers: getHeaders(storeHandle),
      body: JSON.stringify(payload),
    });
    
    await handleApiError(response);
    const data = await response.json();
    
    console.log('✅ Line item added:', data);
    return data;
  }
  
  // Update line item
  async updateLineItem(
    cartId: string, 
    lineItemId: string, 
    payload: UpdateLineItemPayload,
    storeHandle:string
  ): Promise<CartResponse> {
    const url = `${this.baseUrl}/store/carts/${cartId}/line-items/${lineItemId}`;
    
    console.log('📝 Updating line item:', { url, payload });
    
    const response = await fetch(url, {
      method: 'POST',
      headers: getHeaders(storeHandle),
      body: JSON.stringify(payload),
    });
    
    await handleApiError(response);
    const data = await response.json();
    
    console.log('✅ Line item updated:', data);
    return data;
  }
  
  // Remove line item
  async removeLineItem(cartId: string, lineItemId: string,storeHandle:string): Promise<CartResponse> {
    const url = `${this.baseUrl}/store/carts/${cartId}/line-items/${lineItemId}`;
    
    console.log('🗑️ Removing line item:', url);
    
    const response = await fetch(url, {
      method: 'DELETE',
      headers: getHeaders(storeHandle),
    });
    
    await handleApiError(response);
    const data = await response.json();
    
    console.log('✅ Line item removed:', data);
    return data;
  }
}

// Export singleton instance
export const cartAPI = new CartAPI();
