'use client';

import React, { useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useProductStore } from '@/store/productStore';
import { ImageUpload } from '@/components/ui/ImageUploadBasic';
import { RichTextEditor } from '@/components/ui/RichTextEditorBasic';
import { ProductOptions } from './ProductOptionsBasic';
import { ProductVariants } from './ProductVariantsBasic';
import { medusaAdminService } from '@/lib/api/medusa-admin';
import { useToast } from '@/app/providers/toast-provider';

const steps = [
  'Basic Information',
  'Images & Media',
  'Options & Variants',
  'Content & SEO',
  'Review & Update',
];



export const EditProductForm: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const storeHandle = params.storeHandle as string;
  const productId = params.productId as string;
  const { showToast } = useToast();
  const [categories, setCategories] = React.useState<any[]>([]);
  const [collections, setCollections] = React.useState<any[]>([]);
  const [tags, setTags] = React.useState<any[]>([]);
  const [isLoadingData, setIsLoadingData] = React.useState(false);

  const {
    formData,
    currentStep,
    isLoading,
    errors,
    isEditMode,
    setCurrentStep,
    updateField,
    addOption,
    updateOption,
    removeOption,
    addVariant,
    updateVariant,
    removeVariant,
    generateVariantsFromOptions,
    addImage,
    removeImage,
    setThumbnail,
    updateProduct,
    resetForm,
    setErrors,
    setEditMode,
    loadProductForEdit,
  } = useProductStore();

  // Load categories, collections, and tags
  useEffect(() => {
    const loadData = async () => {
      setIsLoadingData(true);
      try {
        const [categoriesData, collectionsData, tagsData] = await Promise.all([
          medusaAdminService.getCategories(storeHandle),
          medusaAdminService.getCollections(storeHandle),
          medusaAdminService.getTags(storeHandle)
        ]);
        
        setCategories(categoriesData || []);
        setCollections(collectionsData || []);
        setTags(tagsData || []);
      } catch (error) {
        console.error('Error loading form data:', error);
        showToast('Failed to load form data', 'error');
      } finally {
        setIsLoadingData(false);
      }
    };
    
    if (storeHandle) {
      loadData();
    }
  }, [storeHandle, showToast]);

  // Load product data when component mounts
  useEffect(() => {
    const loadProduct = async () => {
      setEditMode(true, productId);
      const success = await loadProductForEdit(productId);
      if (!success) {
        showToast('Failed to load product data', 'error');
        router.push(`/${storeHandle}/admin/products`);
      }
    };

    loadProduct();

    // Cleanup on unmount
    return () => {
      resetForm();
    };
  }, [productId, setEditMode, loadProductForEdit, resetForm, router, storeHandle, showToast]);

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleUpdate = async () => {
    try {
      console.log("formData::::::::<><><>",formData);
     
      const success = await updateProduct();
      if (success) {
        showToast('Product updated successfully', 'success');
        router.push(`/${storeHandle}/admin/products`);
      } else {
        showToast('Failed to update product', 'error');
      }
    } catch (error) {
      console.error('Error updating product:', error);
      showToast('Failed to update product', 'error');
    }
  };

  const handleBackToProducts = () => {
    resetForm();
    router.push(`/${storeHandle}/admin/products`);
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Name *
              </label>
              <input
                type="text"
                value={formData.productName || ''}
                onChange={(e) => updateField('productName', e.target.value)}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.productName ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter product name"
              />
              {errors.productName && (
                <p className="text-red-500 text-sm mt-1">{errors.productName}</p>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Handle *
              </label>
              <input
                type="text"
                value={formData.productHandle || ''}
                onChange={(e) => updateField('productHandle', e.target.value)}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.productHandle ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="product-handle"
              />
              {errors.productHandle && (
                <p className="text-red-500 text-sm mt-1">{errors.productHandle}</p>
              )}
              <p className="text-gray-500 text-sm mt-1">URL-friendly version of product name</p>
            </div>
            
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Subtitle
              </label>
              <input
                type="text"
                value={formData.productSubtitle || ''}
                onChange={(e) => updateField('productSubtitle', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Short description that appears below the product name"
              />
            </div>
            
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Description *
              </label>
              <textarea
                value={formData.productDescription || ''}
                onChange={(e) => updateField('productDescription', e.target.value)}
                rows={4}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.productDescription ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Describe your product..."
              />
              {errors.productDescription && (
                <p className="text-red-500 text-sm mt-1">{errors.productDescription}</p>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Category *
              </label>
              <select
                value={formData.productCategory || ''}
                onChange={(e) => updateField('productCategory', e.target.value)}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.productCategory ? 'border-red-500' : 'border-gray-300'
                }`}
                disabled={isLoadingData}
              >
                <option value="">Select a category</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
              {errors.productCategory && (
                <p className="text-red-500 text-sm mt-1">{errors.productCategory}</p>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Collection
              </label>
              <select
                value={formData.productCollection || ''}
                onChange={(e) => updateField('productCollection', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={isLoadingData}
              >
                <option value="">None</option>
                {collections.map((collection) => (
                  <option key={collection.id} value={collection.id}>
                    {collection.title}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Tags
              </label>
              <div className="space-y-2">
                <div className="flex flex-wrap gap-2">
                  {(formData.productTags || []).map((tag, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
                    >
                      {tag}
                      <button
                        onClick={() => {
                          const newTags = [...(formData.productTags || [])];
                          newTags.splice(index, 1);
                          updateField('productTags', newTags);
                        }}
                        className="ml-2 text-blue-600 hover:text-blue-800"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
                <select
                  onChange={(e) => {
                    if (e.target.value && !(formData.productTags || []).includes(e.target.value)) {
                      updateField('productTags', [...(formData.productTags || []), e.target.value]);
                    }
                    e.target.value = '';
                  }}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  disabled={isLoadingData}
                >
                  <option value="">{isLoadingData ? 'Loading tags...' : 'Select tags'}</option>
                  {tags.map((tag) => (
                    <option key={tag.id} value={tag.value}>
                      {tag.value}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        );

      case 1:
        return (
          <div>
            <ImageUpload
              images={formData.productImages || []}
              onImagesChange={(images) => updateField('productImages', images)}
              thumbnailId={formData.productThumbnail?.id}
              onThumbnailChange={(imageId) => {
                const image = formData.productImages?.find(img => img.id === imageId);
                if (image) {
                  setThumbnail(image);
                }
              }}
              label="Product Images *"
              error={errors.productImages}
              helperText="Upload high-quality images of your product. The first image will be used as the thumbnail."
            />
          </div>
        );

      case 2:
        return (
          <div className="space-y-8">
            <ProductOptions
              options={formData.productOptions || []}
              onChange={(options) => updateField('productOptions', options)}
              onGenerateVariants={generateVariantsFromOptions}
              error={errors.productOptions}
            />
            <div className="border-t border-gray-200 pt-8">
              <ProductVariants
                variants={formData.productVariants || []}
                onChange={(variants) => updateField('productVariants', variants)}
                error={errors.productVariants}
              />
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <RichTextEditor
              label="Product Overview"
              value={formData.productOverview || ''}
              onChange={(value) => updateField('productOverview', value)}
              placeholder="Provide a detailed overview of your product..."
              height={200}
            />
            <RichTextEditor
              label="Product Features"
              value={formData.productFeatures || ''}
              onChange={(value) => updateField('productFeatures', value)}
              placeholder="List the key features and benefits..."
              height={200}
            />
            <RichTextEditor
              label="Product Specifications"
              value={formData.productSpecifications || ''}
              onChange={(value) => updateField('productSpecifications', value)}
              placeholder="Include technical specifications, dimensions, materials, etc..."
              height={200}
            />
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-blue-800">Review all product changes before updating. Your changes will be saved to the existing product.</span>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold mb-3">Basic Information</h3>
                <div className="space-y-2 text-sm">
                  <p><strong>Name:</strong> {formData.productName}</p>
                  <p><strong>Handle:</strong> {formData.productHandle}</p>
                  <p><strong>Category:</strong> {categories.find(c => c.id === formData.productCategory)?.name || 'None'}</p>
                  <p><strong>Collection:</strong> {collections.find(c => c.id === formData.productCollection)?.title || 'None'}</p>
                  <p><strong>Status:</strong> <span className="capitalize">{formData.status}</span></p>
                </div>
              </div>
              
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold mb-3">Pricing & Inventory</h3>
                <div className="space-y-2 text-sm">
                  <p><strong>Variants:</strong> {formData.productVariants?.length || 0}</p>
                  <p><strong>Starting Price:</strong> ₹{formData.productSalePrice || 0}</p>
                  <p><strong>Total Stock:</strong> {formData.productStock || 0}</p>
                </div>
              </div>
              
              <div className="md:col-span-2 bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold mb-3">Images</h3>
                <div className="space-y-2 text-sm">
                  <p><strong>Product Images:</strong> {formData.productImages?.length || 0} uploaded</p>
                  <p><strong>Thumbnail:</strong> {formData.productThumbnail ? 'Set' : 'Not set'}</p>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  // Show loading state while loading product data
  if (isLoading && !formData.productName) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <svg className="animate-spin w-8 h-8 text-blue-600 mx-auto mb-4" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <p className="text-gray-600">Loading product data...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="mb-6">
        <button
          onClick={handleBackToProducts}
          className="flex items-center text-gray-600 hover:text-gray-800 mb-4"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Back to Products
        </button>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Edit Product</h1>
        <p className="text-gray-600">Update your product information, images, and variants.</p>
      </div>

      {/* Stepper */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div key={step} className="flex items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  index <= currentStep
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}
              >
                {index + 1}
              </div>
              <span
                className={`ml-2 text-sm font-medium ${
                  index <= currentStep ? 'text-blue-600' : 'text-gray-500'
                }`}
              >
                {step}
              </span>
              {index < steps.length - 1 && (
                <div
                  className={`w-12 h-0.5 mx-4 ${
                    index < currentStep ? 'bg-blue-600' : 'bg-gray-200'
                  }`}
                />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Form Content */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <h2 className="text-xl font-semibold mb-6">{steps[currentStep]}</h2>
        {renderStepContent(currentStep)}
      </div>

      {/* Navigation */}
      <div className="flex justify-between items-center">
        <button
          onClick={handleBack}
          disabled={currentStep === 0}
          className="flex items-center px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Back
        </button>

        <div className="flex space-x-3">
          {currentStep === steps.length - 1 ? (
            <button
              onClick={handleUpdate}
              disabled={isLoading}
              className="flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              {isLoading ? (
                <svg className="animate-spin w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                </svg>
              )}
              Update Product
            </button>
          ) : (
            <button
              onClick={handleNext}
              className="flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Next
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};