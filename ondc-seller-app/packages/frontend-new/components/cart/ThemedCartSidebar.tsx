'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
// import { useCartContext } from './CartProvider';
import { useApiCartContext } from './ApiCartProvider';
// import { formatPrice, calculateCartTotals } from '@/lib/cart/mockCartData';

interface ThemedCartSidebarProps {
  storeHandle?: string;
}

export const ThemedCartSidebar: React.FC<ThemedCartSidebarProps> = ({ storeHandle }) => {
  const router = useRouter();
  const { cart, isOpen, isLoading, closeCart, removeItem, updateQuantity } = useApiCartContext();
  const [isCheckingOut, setIsCheckingOut] = useState(false);
  
  // Calculate totals for API cart
  const formatPrice = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount / 100); // Assuming amount is in cents
  };
  
  const calculateTotals = () => {
    if (!cart || !cart.items || cart.items.length === 0) {
      return {
        subtotal: 0,
        tax: 0,
        shipping: 0,
        finalTotal: 0,
        totalItems: 0
      };
    }
    
    const subtotal = cart.items.reduce((sum, item) => {
      return sum + (item.unit_price * item.quantity);
    }, 0);
    
    const tax = subtotal * 0.08; // 8% tax
    const shipping = subtotal > 5000 ? 0 : 500; // Free shipping over $50
    const finalTotal = subtotal + tax + shipping;
    const totalItems = cart.items.reduce((sum, item) => sum + item.quantity, 0);
    
    return {
      subtotal,
      tax,
      shipping,
      finalTotal,
      totalItems
    };
  };
  
  const totals = calculateTotals();

  const handleCheckout = async () => {
    setIsCheckingOut(true);
    
    try {
      console.log('Proceeding to checkout with:', cart);
      console.log('Total amount:', formatPrice(totals.finalTotal));
      
      // Close cart and navigate to checkout page
      closeCart();
      
      // Navigate to checkout page
      const checkoutUrl = storeHandle ? `/${storeHandle}/checkout` : '/checkout';
      router.push(checkoutUrl);
      
    } catch (error) {
      console.error('Checkout error:', error);
      alert('Checkout failed. Please try again.');
    } finally {
      setIsCheckingOut(false);
    }
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop Overlay with Blur */}
      <div 
        className="fixed inset-0 z-50 transition-all duration-300 ease-in-out"
        style={{ 
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          backdropFilter: 'blur(8px)',
          WebkitBackdropFilter: 'blur(8px)',
        }}
        onClick={closeCart}
      />

      {/* Cart Sidebar */}
      <div 
        className={`fixed top-0 right-0 h-full w-full max-w-md z-50 transform transition-transform duration-300 ease-in-out shadow-2xl flex flex-col ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
        style={{ 
          backgroundColor: 'var(--theme-surface, #ffffff)',
        }}
      >
        {/* Header */}
        <div 
          className="flex items-center justify-between p-6 border-b"
          style={{ borderBottomColor: 'var(--theme-border, #e5e7eb)' }}
        >
          <div className="flex items-center space-x-3">
            <div 
              className="w-8 h-8 rounded-full flex items-center justify-center"
              style={{ backgroundColor: 'var(--theme-primary, #3b82f6)' }}
            >
              <svg 
                className="w-5 h-5" 
                fill="none" 
                stroke="var(--btn-text, #ffffff)" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
              </svg>
            </div>
            <div>
              <h2 
                className="text-lg font-bold"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                Shopping Cart
              </h2>
              <p 
                className="text-sm"
                style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
              >
                {totals.totalItems} {totals.totalItems === 1 ? 'item' : 'items'}
              </p>
            </div>
          </div>
          
          <button
            onClick={closeCart}
            className="p-2 rounded-full transition-colors hover:opacity-80"
            style={{ backgroundColor: 'var(--theme-background, #f3f4f6)' }}
          >
            <svg 
              className="w-5 h-5" 
              fill="none" 
              stroke="var(--theme-text, #111827)" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Cart Items */}
        <div className="flex-1 overflow-y-auto p-6 space-y-4 cart-items min-h-0">
          {!cart || !cart.items || cart.items.length === 0 ? (
            <div className="text-center py-12">
              <div 
                className="w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4"
                style={{ backgroundColor: 'var(--theme-background, #f3f4f6)' }}
              >
                <svg 
                  className="w-8 h-8" 
                  fill="none" 
                  stroke="var(--theme-text-secondary, #6b7280)" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
                </svg>
              </div>
              <h3 
                className="text-lg font-medium mb-2"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                Your cart is empty
              </h3>
              <p 
                className="text-sm"
                style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
              >
                Add some products to get started
              </p>
            </div>
          ) : (
            cart.items.map((item) => (
              <div 
                key={item.id} 
                className="flex space-x-4 p-4 rounded-lg border transition-all duration-200 hover:shadow-sm"
                style={{ 
                  backgroundColor: 'var(--theme-surface, #ffffff)',
                  borderColor: 'var(--theme-border, #e5e7eb)',
                }}
              >
                {/* Product Image */}
                <div className="relative w-16 h-16 rounded-lg overflow-hidden flex-shrink-0 bg-gray-100">
                  <Image
                    src={item.image}
                    alt={item.title}
                    fill
                    className="object-cover"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                </div>

                {/* Product Details */}
                <div className="flex-1 min-w-0">
                  <h3 
                    className="font-medium text-sm truncate"
                    style={{ color: 'var(--theme-text, #111827)' }}
                  >
                    {item.title}
                  </h3>
                  
                  {item.variant && (
                    <p 
                      className="text-xs mt-1"
                      style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                    >
                      {item.variant.title || 'Default Variant'}
                    </p>
                  )}

                  <div className="flex items-center justify-between mt-2">
                    <span 
                      className="font-semibold text-sm"
                      style={{ color: 'var(--theme-primary, #3b82f6)' }}
                    >
                      {formatPrice(item.unit_price)}
                    </span>

                    {/* Quantity Controls */}
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => updateQuantity(item.id, item.quantity - 1)}
                        className="w-6 h-6 rounded-full flex items-center justify-center transition-colors hover:opacity-80"
                        style={{ 
                          backgroundColor: 'var(--theme-background, #f3f4f6)',
                          color: 'var(--theme-text, #111827)',
                        }}
                        disabled={isLoading}
                      >
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                        </svg>
                      </button>
                      
                      <span 
                        className="text-sm font-medium min-w-[20px] text-center"
                        style={{ color: 'var(--theme-text, #111827)' }}
                      >
                        {item.quantity}
                      </span>
                      
                      <button
                        onClick={() => updateQuantity(item.id, item.quantity + 1)}
                        className="w-6 h-6 rounded-full flex items-center justify-center transition-colors hover:opacity-80"
                        style={{ 
                          backgroundColor: 'var(--theme-background, #f3f4f6)',
                          color: 'var(--theme-text, #111827)',
                        }}
                        disabled={isLoading}
                      >
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>

                {/* Remove Button */}
                <button
                  onClick={() => removeItem(item.id)}
                  className="p-1 rounded-full transition-colors hover:opacity-80 flex-shrink-0"
                  style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                  disabled={isLoading}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            ))
          )}
        </div>

        {/* Cart Summary & Checkout */}
        {cart && cart.items && cart.items.length > 0 && (
          <div 
            className="border-t p-6 space-y-4 flex-shrink-0"
            style={{ borderTopColor: 'var(--theme-border, #e5e7eb)' }}
          >
            {/* Totals */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span style={{ color: 'var(--theme-text-secondary, #6b7280)' }}>Subtotal:</span>
                <span style={{ color: 'var(--theme-text, #111827)' }}>{formatPrice(totals.subtotal)}</span>
              </div>
              
              <div className="flex justify-between text-sm">
                <span style={{ color: 'var(--theme-text-secondary, #6b7280)' }}>Tax:</span>
                <span style={{ color: 'var(--theme-text, #111827)' }}>{formatPrice(totals.tax)}</span>
              </div>
              
              <div className="flex justify-between text-sm">
                <span style={{ color: 'var(--theme-text-secondary, #6b7280)' }}>Shipping:</span>
                <span style={{ color: 'var(--theme-text, #111827)' }}>
                  {totals.shipping === 0 ? 'Free' : formatPrice(totals.shipping)}
                </span>
              </div>
              
              <div 
                className="flex justify-between text-lg font-bold pt-2 border-t"
                style={{ borderTopColor: 'var(--theme-border, #e5e7eb)' }}
              >
                <span style={{ color: 'var(--theme-text, #111827)' }}>Total:</span>
                <span style={{ color: 'var(--theme-primary, #3b82f6)' }}>{formatPrice(totals.finalTotal)}</span>
              </div>
            </div>

            {/* Checkout Button */}
            <button
              className={`w-full py-3 px-4 rounded-lg font-semibold transition-all duration-200 ${
                isCheckingOut || isLoading
                  ? 'opacity-50 cursor-not-allowed'
                  : 'hover:shadow-lg'
              }`}
              style={{ 
                backgroundColor: isCheckingOut || isLoading 
                  ? 'var(--theme-text-secondary, #6b7280)' 
                  : 'var(--btn-primary, #3b82f6)',
                color: 'var(--btn-text, #ffffff)',
              }}
              disabled={isCheckingOut || isLoading}
              onClick={handleCheckout}
              onMouseEnter={(e) => {
                if (!isCheckingOut && !isLoading) {
                  e.currentTarget.style.backgroundColor = 'var(--btn-primary-hover, #2563eb)';
                }
              }}
              onMouseLeave={(e) => {
                if (!isCheckingOut && !isLoading) {
                  e.currentTarget.style.backgroundColor = 'var(--btn-primary, #3b82f6)';
                }
              }}
            >
              {isCheckingOut 
                ? 'Processing...' 
                : isLoading 
                  ? 'Loading...' 
                  : `Checkout • ${formatPrice(totals.finalTotal)}`
              }
            </button>

            {/* Continue Shopping */}
            <button
              onClick={closeCart}
              className="w-full py-2 px-4 text-sm font-medium transition-colors"
              style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
            >
              Continue Shopping
            </button>
          </div>
        )}
      </div>
    </>
  );
};