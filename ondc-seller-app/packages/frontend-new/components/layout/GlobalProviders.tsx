'use client';

import React, { ReactNode } from 'react';
import { StoreConfigProvider } from '../store/StoreConfigProvider';
import { NavigationProvider } from '../navigation/NavigationProvider';
import { AuthProvider } from '../auth/AuthProvider';
// import { CartProvider } from '../cart/CartProvider';
// import { ApiCartProvider } from '../cart/ApiCartProvider';
import { SimpleCartSidebar } from '../cart/SimpleCartSidebar';
import { NavigationLoader } from '../navigation/NavigationLoader';

interface GlobalProvidersProps {
  children: ReactNode;
  storeHandle: string;
  includeAuth?: boolean;
  includeCart?: boolean;
}

export const GlobalProviders: React.FC<GlobalProvidersProps> = ({ 
  children, 
  storeHandle,
  includeAuth = true,
  includeCart = true,
}) => {
  let content = children;

  // Add cart sidebar without provider (direct API calls)
  if (includeCart) {
    content = (
      <>
        {content}
        <SimpleCartSidebar storeHandle={storeHandle} />
      </>
    );
  }

  // Wrap with AuthProvider if needed
  if (includeAuth) {
    content = <AuthProvider>{content}</AuthProvider>;
  }

  return (
    <StoreConfigProvider storeHandle={storeHandle}>
      <NavigationProvider>
        {content}
        <NavigationLoader />
      </NavigationProvider>
    </StoreConfigProvider>
  );
};