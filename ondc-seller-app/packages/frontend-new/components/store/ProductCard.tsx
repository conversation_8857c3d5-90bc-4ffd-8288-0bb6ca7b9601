import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Product } from '@/types';
import { cartAPI, calculateCartMetadata, getStoredCartId, setStoredCartId } from '@/lib/api/cart';
import { useToast } from '@/app/providers/toast-provider';

interface ProductCardProps {
  product: any;
  storeHandle: string;
}

export const ProductCard: React.FC<ProductCardProps> = ({ product, storeHandle }) => {
  const [isLoading, setIsLoading] = useState(false);
  const { showToast } = useToast();
  const defaultImage =  `https://picsum.photos/300/350?random=${product.id}`  ;
  const productImage = product.thumbnail || product.images?.[0]?.url || defaultImage;
  
  // Get the lowest price from variants
  const getLowestPrice = () => {
    if (!product.variants || product.variants.length === 0) return null;
    
    const prices = product.variants.flatMap(variant => 
      variant.prices.map(price => price.amount)
    );
    
    return Math.min(...prices);
  };

  const lowestPrice = getLowestPrice();
  const currency = product.variants?.[0]?.prices?.[0]?.currency_code || 'USD';

  const formatPrice = (amount: number, currencyCode: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode.toUpperCase(),
    }).format(amount);
  };
  
  // Check if product is in stock
  const isInStock = product?.variants?.some(v => v?.metadata?.product_quantity > 0);
  
  // Handle add to cart - DIRECT API CALL
  const handleAddToCart = async (e: React.MouseEvent) => {
    if (!isInStock || isLoading) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Get the first available variant
      const availableVariant = product?.variants?.find(v => v?.metadata?.product_quantity > 0);
      
      if (!availableVariant) {
        showToast('No available variant found', 'error');
        return;
      }
      
      // Get or create cart
      let cartId = getStoredCartId();
      
      if (!cartId) {
        // Create new cart if none exists
        const regionsResponse = await cartAPI.getRegions('Asia', storeHandle);
        const region = regionsResponse.regions.find(r => 
          r.name.toLowerCase().includes('asia')
        ) || regionsResponse.regions[0];
        
        if (!region) {
          throw new Error('No region available');
        }
        
        const cartResponse = await cartAPI.createCart({ region_id: region.id }, storeHandle);
        cartId = cartResponse.cart.id;
        setStoredCartId(cartId);
      }
      
      // Calculate metadata
      const metadata = calculateCartMetadata(
        lowestPrice || 0,
        1,
        0.08 // 8% tax
      );
      
      // Add item to cart
      await cartAPI.addLineItem(cartId, {
        variant_id: availableVariant.id,
        quantity: 1,
        metadata,
      }, storeHandle);
      
      showToast('Product added to cart successfully!', 'success');
      
      // Optionally trigger a custom event to update cart count in header
      window.dispatchEvent(new CustomEvent('cartUpdated'));
      
    } catch (error) {
      console.error('Error adding to cart:', error);
      showToast('Failed to add product to cart', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div 
      // href={`/${storeHandle}/products/${product.id}`}
      className="group block h-full"
    >
      <div 
        className="bg-white h-full rounded-lg shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden flex flex-col"
        style={{ 
          // backgroundColor: 'var(--theme-background, white)',
          borderColor: 'var(--theme-border, #e5e7eb)',
        }}
      >
        {/* Product Image */}
        <Link 
          href={`/${storeHandle}/products/${product.id}`}
        >
          <div className="relative aspect-square overflow-hidden">
            <Image
              src={productImage}
              alt={product.title}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-300"
              sizes="(max-width: 480px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, (max-width: 1536px) 25vw, 20vw"
            />
            
            {/* Product Status Badge */}
            {product.status === 'draft' && (
              <div className="absolute top-2 left-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded-md font-medium">
                Draft
              </div>
            )}
            
            {/* Quick View Button - Hidden on mobile */}
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
              <button className="hidden sm:block bg-white text-gray-800 px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transform translate-y-2 group-hover:translate-y-0 transition-all duration-200 font-medium text-sm">
                Quick View
              </button>
            </div>
          </div>
        </Link>
        
        {/* Product Content */}
        <div className="p-3 sm:p-4 flex-1 flex flex-col">
          {/* Product Title */}
          <Link 
            href={`/${storeHandle}/products/${product.id}`}
          >
            
            <h3 
              className="font-semibold mb-2 line-clamp-2 group-hover:opacity-80 transition-colors text-sm sm:text-base"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              {product.title}
            </h3>
          </Link>
          
          {/* Product Description - Hidden on mobile */}
          {product.description && (
            <p 
              className="hidden sm:block text-xs sm:text-sm mb-3 line-clamp-2"
              style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
            >
              {product.description}
            </p>
          )}
          
          {/* Product Tags - Limited on mobile */}
          {product?.tags && product?.tags?.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-3">
              {product?.tags.slice(0, 1).map((tag) => (
                <span 
                  key={tag?.id}
                  className="text-xs px-2 py-1 rounded-md"
                  style={{ 
                    backgroundColor: 'var(--theme-surface, #f3f4f6)',
                    color: 'var(--theme-text-secondary, #6b7280)',
                  }}
                >
                  {tag?.value}
                </span>
              ))}
              {product?.tags.length > 1 && (
                <span 
                  className="text-xs"
                  style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                >
                  +{product?.tags.length - 1}
                </span>
              )}
            </div>
          )}
          
          {/* Spacer to push price and button to bottom */}
          <div className="flex-1"></div>
          
          {/* Price and Actions */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <div className="flex flex-col">
              {product?.metadata?.additional_data?.product_prices[0]?.original_price && (
                <span 
                  className="text-base sm:text-lg font-bold"
                  style={{ color: 'var(--theme-text, #111827)' }}
                >
                  {formatPrice(product?.metadata?.additional_data?.product_prices[0]?.sale_price, 'inr')}
                </span>
              )}
              {product?.variants && product?.variants.length > 0 && (
                <span 
                  className="text-xs"
                  style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                >
                  {product?.variants.length} variant{product?.variants.length !== 1 ? 's' : ''}
                </span>
              )}
            </div>
            
            <button 
              className={`w-full sm:w-auto px-3 py-2 rounded-md text-xs sm:text-sm font-medium transition-all duration-200 hover:shadow-sm ${
                !isInStock || isLoading ? 'opacity-50 cursor-not-allowed' : ''
              }`}
              style={{ 
                backgroundColor: (isInStock && !isLoading) ? 'var(--btn-primary, #3b82f6)' : 'var(--theme-text-secondary, #6b7280)',
                color: 'var(--btn-text, white)',
              }}
              onMouseEnter={(e) => {
                if (isInStock && !isLoading) {
                  e.currentTarget.style.backgroundColor = 'var(--btn-primary-hover, #2563eb)';
                }
              }}
              onMouseLeave={(e) => {
                if (isInStock && !isLoading) {
                  e.currentTarget.style.backgroundColor = 'var(--btn-primary, #3b82f6)';
                }
              }}
              onClick={handleAddToCart}
              disabled={!isInStock || isLoading}
            >
              {isLoading ? 'Adding...' : 'Add to Cart'}
            </button>
          </div>
          
          {/* Inventory Status */}
          {product?.variants && product?.variants?.length > 0 && (
            <div className="mt-2">
              {product?.variants?.some(v => v?.metadata?.product_quantity > 0) ? (
                <span 
                  className="text-xs flex items-center"
                  style={{ color: 'var(--theme-accent, #10b981)' }}
                >
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                  In Stock
                </span>
              ) : (
                <span 
                  className="text-xs flex items-center"
                  style={{ color: '#ef4444' }}
                >
                  <div className="w-2 h-2 bg-red-500 rounded-full mr-1"></div>
                  Out of Stock
                </span>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};