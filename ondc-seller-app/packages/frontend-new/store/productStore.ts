import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { ProductFormData, ProductOption, ProductVariant, ProductImage } from '@/lib/validations/product';
import { medusaAdminService } from '@/lib/api/medusa-admin';

interface ProductStore {
  // Form Data
  formData: Partial<ProductFormData>;
  
  // UI State
  isLoading: boolean;
  errors: Record<string, string>;
  currentStep: number;
  isEditMode: boolean;
  productId: string | null;
  
  // Actions
  setFormData: (data: Partial<ProductFormData>) => void;
  updateField: (field: keyof ProductFormData, value: any) => void;
  
  // Options Management
  addOption: () => void;
  updateOption: (index: number, option: ProductOption) => void;
  removeOption: (index: number) => void;
  
  // Variants Management
  addVariant: () => void;
  updateVariant: (index: number, variant: ProductVariant) => void;
  removeVariant: (index: number) => void;
  generateVariantsFromOptions: () => void;
  
  // Images Management
  addImage: (image: ProductImage) => void;
  updateImage: (index: number, image: ProductImage) => void;
  removeImage: (index: number) => void;
  setThumbnail: (image: ProductImage) => void;
  
  // Form Management
  setErrors: (errors: Record<string, string>) => void;
  clearErrors: () => void;
  setLoading: (loading: boolean) => void;
  setCurrentStep: (step: number) => void;
  
  // Reset
  resetForm: () => void;
  
  // Edit Mode
  setEditMode: (isEdit: boolean, productId?: string) => void;
  loadProductForEdit: (productId: string) => Promise<boolean>;
  
  // Save/Submit
  saveProduct: (status?: string) => Promise<boolean>;
  updateProduct: () => Promise<boolean>;
}

const initialFormData: Partial<ProductFormData> = {
  productName: '',
  productHandle: '',
  productDescription: '',
  productSubtitle: '',
  productCategory: '',
  productCollection: '',
  productTags: [],
  productImages: [],
  productThumbnail: undefined,
  productOptions: [],
  productVariants: [],
  productOriginalPrice: 0,
  productSalePrice: 0,
  productStock: 0,
  productOverview: '',
  productFeatures: '',
  productSpecifications: '',
  status: 'draft',
};

const generateId = () => Math.random().toString(36).substr(2, 9);

export const useProductStore = create<ProductStore>()(
  devtools(
    (set, get) => ({
      formData: initialFormData,
      isLoading: false,
      errors: {},
      currentStep: 0,
      isEditMode: false,
      productId: null,

      setFormData: (data) =>
        set((state) => ({
          formData: { ...state.formData, ...data },
        })),

      updateField: (field, value) =>
        set((state) => ({
          formData: { ...state.formData, [field]: value },
        })),

      // Options Management
      addOption: () =>
        set((state) => {
          const newOption: ProductOption = {
            id: generateId(),
            name: '',
            values: [''],
          };
          return {
            formData: {
              ...state.formData,
              productOptions: [...(state.formData.productOptions || []), newOption],
            },
          };
        }),

      updateOption: (index, option) =>
        set((state) => {
          const options = [...(state.formData.productOptions || [])];
          options[index] = option;
          return {
            formData: { ...state.formData, productOptions: options },
          };
        }),

      removeOption: (index) =>
        set((state) => {
          const options = [...(state.formData.productOptions || [])];
          options.splice(index, 1);
          return {
            formData: { ...state.formData, productOptions: options },
          };
        }),

      // Variants Management
      addVariant: () =>
        set((state) => {
          const newVariant: ProductVariant = {
            id: generateId(),
            title: '',
            sku: '',
            originalPrice: 0,
            salePrice: 0,
            stock: 0,
            stockStatus: 'in_stock',
          };
          return {
            formData: {
              ...state.formData,
              productVariants: [...(state.formData.productVariants || []), newVariant],
            },
          };
        }),

      updateVariant: (index, variant) =>
        set((state) => {
          const variants = [...(state.formData.productVariants || [])];
          variants[index] = variant;
          
          // Update main product pricing from first variant
          let updatedFormData = { ...state.formData, productVariants: variants };
          if (index === 0 && variants[0]) {
            updatedFormData = {
              ...updatedFormData,
              productOriginalPrice: variants[0].originalPrice,
              productSalePrice: variants[0].salePrice,
              productStock: variants.reduce((total, v) => total + v.stock, 0),
            };
          }
          
          return { formData: updatedFormData };
        }),

      removeVariant: (index) =>
        set((state) => {
          const variants = [...(state.formData.productVariants || [])];
          variants.splice(index, 1);
          
          // Update main product pricing
          let updatedFormData = { ...state.formData, productVariants: variants };
          if (variants.length > 0) {
            updatedFormData = {
              ...updatedFormData,
              productOriginalPrice: variants[0].originalPrice,
              productSalePrice: variants[0].salePrice,
              productStock: variants.reduce((total, v) => total + v.stock, 0),
            };
          }
          
          return { formData: updatedFormData };
        }),

      generateVariantsFromOptions: () =>
        set((state) => {
          const options = state.formData.productOptions || [];
          if (options.length === 0) return state;

          // Generate all combinations of option values
          const combinations: Record<string, string>[] = [];
          
          const generateCombinations = (optionIndex: number, currentCombination: Record<string, string>) => {
            if (optionIndex >= options.length) {
              combinations.push({ ...currentCombination });
              return;
            }
            
            const option = options[optionIndex];
            option.values.forEach((value) => {
              generateCombinations(optionIndex + 1, {
                ...currentCombination,
                [option.name]: value,
              });
            });
          };
          
          generateCombinations(0, {});
          
          // Create variants from combinations
          const variants: ProductVariant[] = combinations.map((combination) => ({
            id: generateId(),
            title: Object.values(combination).join(' / '),
            sku: '',
            originalPrice: 0,
            salePrice: 0,
            stock: 0,
            stockStatus: 'in_stock' as const,
            options: combination,
          }));
          
          return {
            formData: { ...state.formData, productVariants: variants },
          };
        }),

      // Images Management
      addImage: (image) =>
        set((state) => ({
          formData: {
            ...state.formData,
            productImages: [...(state.formData.productImages || []), image],
          },
        })),

      updateImage: (index, image) =>
        set((state) => {
          const images = [...(state.formData.productImages || [])];
          images[index] = image;
          return {
            formData: { ...state.formData, productImages: images },
          };
        }),

      removeImage: (index) =>
        set((state) => {
          const images = [...(state.formData.productImages || [])];
          images.splice(index, 1);
          return {
            formData: { ...state.formData, productImages: images },
          };
        }),

      setThumbnail: (image) =>
        set((state) => ({
          formData: { ...state.formData, productThumbnail: image },
        })),

      // Form Management
      setErrors: (errors) => set({ errors }),
      clearErrors: () => set({ errors: {} }),
      setLoading: (loading) => set({ isLoading: loading }),
      setCurrentStep: (step) => set({ currentStep: step }),

      resetForm: () =>
        set({
          formData: initialFormData,
          errors: {},
          currentStep: 0,
          isLoading: false,
          isEditMode: false,
          productId: null,
        }),

      setEditMode: (isEdit, productId) =>
        set({
          isEditMode: isEdit,
          productId: productId || null,
        }),

      loadProductForEdit: async (productId) => {
        set({ isLoading: true });
        try {
          // Get store handle from URL
          const storeHandle = window.location.pathname.split('/')[1];
          
          if (!storeHandle) {
            throw new Error('Store handle not found');
          }
          
          console.log('Loading product for edit:', productId);
          
          // Fetch product data from Medusa API
          const product = await medusaAdminService.getProduct(storeHandle, productId);
          
          console.log('Product loaded:', product);
          
          // Transform Medusa product data to form data structure
          const formData: Partial<ProductFormData> = {
            productName: product.title || '',
            productHandle: product.handle || '',
            productDescription: product.description || '',
            productSubtitle: product.metadata?.additional_data?.product_subtitle || '',
            productCategory: product.categories?.[0]?.id || '',
            productCollection: product.collection_id || '',
            productTags: product.tags?.map(tag => tag.value) || [],
            productImages: product.images?.map(image => ({
              id: image.id,
              url: image.url,
              alt: product.title || 'Product image'
            })) || [],
            productThumbnail: product.thumbnail ? {
              id: 'thumbnail',
              url: product.thumbnail,
              alt: product.title || 'Product thumbnail'
            } : undefined,
            productOptions: product.options?.map(option => ({
              id: option.id,
              name: option.title,
              values: option.values?.map(v => v.value) || []
            })) || [],
            productVariants: product.variants?.map(variant => ({
              id: variant.id,
              title: variant.title,
              sku: variant.sku || '',
              originalPrice: variant.metadata?.original_price || variant.prices?.[0]?.amount || 0,
              salePrice: variant.metadata?.sale_price || variant.prices?.[0]?.amount || 0,
              stock: variant.inventory_quantity || 0,
              stockStatus: variant.inventory_quantity > 0 ? 'in_stock' : 'out_of_stock',
              options: variant.options?.reduce((acc, opt) => {
                acc[opt.option_id] = opt.value;
                return acc;
              }, {} as Record<string, string>) || {}
            })) || [],
            productOriginalPrice: product.variants?.[0]?.metadata?.original_price || product.variants?.[0]?.prices?.[0]?.amount || 0,
            productSalePrice: product.variants?.[0]?.metadata?.sale_price || product.variants?.[0]?.prices?.[0]?.amount || 0,
            productStock: product.variants?.reduce((total, variant) => total + (variant.inventory_quantity || 0), 0) || 0,
            productOverview: product.metadata?.additional_data?.product_overview || '',
            productFeatures: product.metadata?.additional_data?.product_features || '',
            productSpecifications: product.metadata?.additional_data?.product_specifications || '',
            status: product.status === 'published' ? 'active' : 'draft',
          };
          
          set({
            formData,
            isEditMode: true,
            productId,
            currentStep: 0,
          });
          
          return true;
        } catch (error) {
          console.error('Error loading product:', error);
          return false;
        } finally {
          set({ isLoading: false });
        }
      },

      saveProduct: async (status?: string) => {
        console.log('=== SAVE PRODUCT FUNCTION CALLED ===');
        set({ isLoading: true });
        try {
          const { formData } = get();
          console.log('Form data in saveProduct:', formData);
          
          // Get store handle from URL or auth store
          const storeHandle = window.location.pathname.split('/')[1];
          console.log('Store handle:', storeHandle);
          
          if (!storeHandle) {
            console.error('Store handle not found');
            throw new Error('Store handle not found');
          }
          
          // Use provided status or fall back to formData status
          const productStatus = status || formData.status || 'draft';
          console.log('Product status:', productStatus);
          
          // Transform form data to Medusa API payload structure
          const productPayload = {
            title: formData.productName || 'Test Product',
            handle: formData.productHandle || 'test-product',
            description: formData.productDescription || 'Test Description',
            status: productStatus === 'active' ? 'published' : 'draft',
            categories: formData.productCategory ? [{ id: formData.productCategory }] : [],
            collection_id: formData.productCollection || '',
            tags: formData.productTags || [],
            options: (formData.productOptions || []).map(option => ({
              title: option.name,
              values: option.values
            })),
            metadata: {
              additional_data: {
                product_prices: formData.productVariants?.map(variant => ({
                  sale_price: variant.salePrice || 0,
                  original_price: variant.originalPrice || 0
                })) || [{
                  sale_price: formData.productSalePrice || 0,
                  original_price: formData.productOriginalPrice || 0
                }],
                product_quantity: formData.productStock || 0,
                product_inventory_status: 'in_stock',
                product_overview: formData.productOverview || '',
                product_features: formData.productFeatures || '',
                product_specifications: formData.productSpecifications || ''
              }
            },
            variants: (formData.productVariants || []).map((variant, index) => ({
              title: variant.title || 'default',
              sku: variant.sku || `default-${formData.productHandle || 'test-product'}-${index + 1}`,
              material: null,
              weight: null,
              width: null,
              length: null,
              height: null,
              metadata: {
                sale_price: variant.salePrice || 0,
                original_price: variant.originalPrice || 0,
                product_quantity: variant.stock || 0,
                product_inventory_status: variant.stockStatus || 'in_stock'
              },
              prices: [{
                currency_code: 'inr',
                amount: variant.originalPrice || 0
              }]
            })),
            images: (formData.productImages || []).map(image => ({
              url: image.url
            })),
            thumbnail: formData.productThumbnail?.url || formData.productImages?.[0]?.url || ''
          };
          
          // If no variants exist, create a default variant
          if (productPayload.variants.length === 0) {
            productPayload.variants = [{
              title: 'default',
              sku: `default-${formData.productHandle || 'test-product'}-001`,
              material: null,
              weight: null,
              width: null,
              length: null,
              height: null,
              metadata: {
                sale_price: formData.productSalePrice || 0,
                original_price: formData.productOriginalPrice || 0,
                product_quantity: formData.productStock || 0,
                product_inventory_status: 'in_stock'
              },
              prices: [{
                currency_code: 'inr',
                amount: formData.productOriginalPrice || 0
              }]
            }];
          }
          
          console.log('=== CREATING PRODUCT ===');
          console.log('Store handle:', storeHandle);
          console.log('Product status:', productStatus);
          console.log('Form data:', formData);
          console.log('Creating product with payload:', productPayload);
          console.log('About to call medusaAdminService.createProduct...');
          
          // Make API call to create product
          console.log('Calling API now...');
          const createdProduct = await medusaAdminService.createProduct(storeHandle, productPayload);
          console.log('API call completed, result:', createdProduct);
          
          console.log('Product created successfully:', createdProduct);
          
          // Reset form after successful save
          get().resetForm();
          
          return true;
        } catch (error) {
          console.error('Error saving product:', error);
          return false;
        } finally {
          set({ isLoading: false });
        }
      },

      updateProduct: async () => {
        set({ isLoading: true });
        try {
          const { formData, productId } = get();
          
          // Get store handle from URL
          const storeHandle = window.location.pathname.split('/')[1];
          
          if (!storeHandle || !productId) {
            throw new Error('Store handle or product ID not found');
          }
          
          console.log('Updating product:', productId, formData);
          
          // Transform form data to Medusa API payload structure
          const productPayload = {
            title: formData.productName || 'Updated Product',
            handle: formData.productHandle || 'updated-product',
            description: formData.productDescription || 'Updated Description',
            status: formData.status === 'active' ? 'published' : 'draft',
           
            metadata: {
              additional_data: {
                product_subtitle: formData.productSubtitle || '',
                product_prices: formData.productVariants?.map(variant => ({
                  sale_price: variant.salePrice || 0,
                  original_price: variant.originalPrice || 0
                })) || [{
                  sale_price: formData.productSalePrice || 0,
                  original_price: formData.productOriginalPrice || 0
                }],
                product_quantity: formData.productStock || 0,
                product_inventory_status: 'in_stock',
                product_overview: formData.productOverview || '',
                product_features: formData.productFeatures || '',
                product_specifications: formData.productSpecifications || ''
              }
            },
            variants: (formData.productVariants || []).map((variant, index) => ({
              ...(variant.id?.startsWith('variant_') && { id: variant.id }),
              title: variant.title || 'default',
              sku: variant.sku || `updated-${formData.productHandle || 'product'}-${index + 1}`,
              material: null,
              weight: null,
              width: null,
              length: null,
              height: null,
              metadata: {
                sale_price: variant.salePrice || 0,
                original_price: variant.originalPrice || 0,
                product_quantity: variant.stock || 0,
                product_inventory_status: variant.stockStatus || 'in_stock'
              },
              prices: [{
                currency_code: 'inr',
                amount: variant.originalPrice || 0
              }]
            })),
            images: (formData.productImages || []).map(image => ({
              url: image.url
            })),
            thumbnail: formData.productThumbnail?.url || formData.productImages?.[0]?.url || ''
          };
          if(formData.productCollection) payload.collection_id = formData.productCollection || '';
          if(formData.productTags.length > 0) payload.tags = formData.productTags || [];
          if(formData.productCategory.length > 0) productPayload.categories = formData.productCategory ? [{ id: formData.productCategory }] : [];
          if(formData.productOptions.length > 0) productPayload.options = (formData.productOptions || []).map(option => ({
            title: option.name,
            values: option.values
          }));
          // If no variants exist, create a default variant
          if (productPayload.variants.length === 0) {
            productPayload.variants = [{
              title: 'default',
              sku: `updated-${formData.productHandle || 'product'}-001`,
              material: null,
              weight: null,
              width: null,
              length: null,
              height: null,
              metadata: {
                sale_price: formData.productSalePrice || 0,
                original_price: formData.productOriginalPrice || 0,
                product_quantity: formData.productStock || 0,
                product_inventory_status: 'in_stock'
              },
              prices: [{
                currency_code: 'inr',
                amount: formData.productOriginalPrice || 0
              }]
            }];
          };
          
          console.log('Updating product with payload:', productPayload);
          
          // Make API call to update product
          const updatedProduct = await medusaAdminService.updateProduct(storeHandle, productId, productPayload);
          
          console.log('Product updated successfully:', updatedProduct);
          
          return true;
        } catch (error) {
          console.error('Error updating product:', error);
          return false;
        } finally {
          set({ isLoading: false });
        }
      },
    }),
    {
      name: 'product-store',
    }
  )
);